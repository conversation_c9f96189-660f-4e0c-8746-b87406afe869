import { useRouter } from 'next/router'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'
import supabase from '@/lib/supabase'
import authTokenManager from '@/lib/auth-token-manager'

export default function ProtectedRoute({ children, adminOnly = false }) {
  const { user, role, loading: authLoading, isAdmin } = useAuth()
  const router = useRouter()
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(true)

  // Handle authentication and authorization
  useEffect(() => {
    // Skip if still loading auth state
    if (authLoading) return

    const checkAccess = async () => {
      try {
        // If no user, redirect to login
        if (!user) {
          const currentPath = encodeURIComponent(router.asPath)
          router.replace(`/admin/login?redirect=${currentPath}`)
          return
        }

        // If adminOnly and user is not admin, show error
        if (adminOnly && !isAdmin) {
          // Special case for known admin users (fallback)
          const knownAdminIds = [
            '8c59a3bc-a96b-4555-bdc4-6abe905ae761', // <EMAIL>
            'c6080246-db51-485e-8e29-69be7cc86cdb'  // <EMAIL>
          ]

          if (knownAdminIds.includes(user.id)) {
            console.log('Using hardcoded admin ID fallback for user:', user.id)
            setIsAuthorized(true)
          } else {
            setError('Unauthorized: Admin access required')
            setIsAuthorized(false)
          }
        } else {
          // User is authenticated and authorized
          setIsAuthorized(true)
          setError(null)
        }

        setLoading(false)
      } catch (error) {
        console.error('Access verification failed:', error)
        setError('Authentication failed: ' + (error?.message || 'Unknown error'))
        setLoading(false)
      }
    }

    checkAccess()
  }, [user, role, authLoading, adminOnly, router, isAdmin])

  // Store session token using the centralized auth token manager
  useEffect(() => {
    if (user && !authLoading) {
      const storeSessionToken = async () => {
        try {
          // Get session directly from Supabase
          const { data, error } = await supabase.auth.getSession()

          if (error) {
            console.warn('Failed to get session:', error)
            return
          }

          if (data?.session?.access_token) {
            // Use the centralized token manager to store the token
            authTokenManager.storeToken(data.session.access_token)
            console.log('Auth token stored using token manager')
          }
        } catch (error) {
          console.warn('Failed to get session token:', error)
        }
      }

      storeSessionToken()
    }
  }, [user, authLoading])

  // Show loading state, error, or children
  if (authLoading || loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <div className="text-gray-600">Authenticating...</div>
      </div>
    )
  }

  if (!isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-red-500 text-center mt-4 px-4 max-w-md">
          <p className="font-semibold">Authorization Error:</p>
          <p>{error || 'Unauthorized access'}</p>
          <button
            onClick={() => router.push('/admin/login')}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Return to Login
          </button>
        </div>
      </div>
    )
  }

  return children
}
