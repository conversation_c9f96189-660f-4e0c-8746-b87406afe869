/**
 * Authentication utilities for the Ocean Soul Sparkles admin panel
 * Provides consistent methods for authenticated API requests and error handling
 */
import supabase from './supabase';
import { toast } from 'react-toastify';
import Router from 'next/router';

/**
 * Error types for authentication failures
 * @readonly
 * @enum {string}
 */
export const AUTH_ERROR_TYPES = {
  TOKEN_EXPIRED: 'token_expired',
  TOKEN_MISSING: 'token_missing',
  INSUFFICIENT_PERMISSIONS: 'insufficient_permissions',
  NETWORK_ERROR: 'network_error',
  SERVER_ERROR: 'server_error',
  UNKNOWN_ERROR: 'unknown_error'
};

/**
 * User-friendly error messages for authentication failures
 * @readonly
 * @enum {string}
 */
export const AUTH_ERROR_MESSAGES = {
  [AUTH_ERROR_TYPES.TOKEN_EXPIRED]: 'Your session has expired. Please log in again.',
  [AUTH_ERROR_TYPES.TOKEN_MISSING]: 'Authentication required. Please log in to continue.',
  [AUTH_ERROR_TYPES.INSUFFICIENT_PERMISSIONS]: 'You don\'t have permission to perform this action.',
  [AUTH_ERROR_TYPES.NETWORK_ERROR]: 'Network error. Please check your connection and try again.',
  [AUTH_ERROR_TYPES.SERVER_ERROR]: 'Server error. Please try again later.',
  [AUTH_ERROR_TYPES.UNKNOWN_ERROR]: 'An unknown error occurred. Please try again.'
};

/**
 * Token refresh state to prevent multiple simultaneous refresh attempts
 * @type {Object}
 */
const tokenRefreshState = {
  isRefreshing: false,
  refreshPromise: null,
  lastRefresh: 0
};

/**
 * Get the current authentication token from Supabase
 * Attempts to refresh the token if it's expired
 * 
 * @async
 * @returns {Promise<string|null>} The authentication token or null if not available
 */
export const getAuthToken = async () => {
  try {
    // Get the current session
    const { data: sessionData } = await supabase.auth.getSession();
    const token = sessionData?.session?.access_token;
    
    if (token) {
      return token;
    }
    
    // If no token, try to refresh
    return await refreshAuthToken();
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

/**
 * Refresh the authentication token
 * Uses a singleton pattern to prevent multiple simultaneous refresh attempts
 * 
 * @async
 * @returns {Promise<string|null>} The refreshed token or null if refresh failed
 */
export const refreshAuthToken = async () => {
  // Prevent multiple simultaneous refresh attempts
  if (tokenRefreshState.isRefreshing) {
    return tokenRefreshState.refreshPromise;
  }
  
  // Check if we've refreshed recently (within the last 10 seconds)
  const now = Date.now();
  if (now - tokenRefreshState.lastRefresh < 10000) {
    // Get the current session again instead of refreshing too frequently
    const { data: sessionData } = await supabase.auth.getSession();
    return sessionData?.session?.access_token || null;
  }
  
  // Set refreshing state
  tokenRefreshState.isRefreshing = true;
  
  // Create a new refresh promise
  tokenRefreshState.refreshPromise = new Promise(async (resolve) => {
    try {
      console.log('Refreshing authentication token...');
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) {
        console.error('Token refresh failed:', error);
        resolve(null);
        return;
      }
      
      const newToken = data?.session?.access_token;
      console.log('Token refreshed successfully');
      
      // Update last refresh timestamp
      tokenRefreshState.lastRefresh = Date.now();
      
      resolve(newToken);
    } catch (error) {
      console.error('Error refreshing token:', error);
      resolve(null);
    } finally {
      tokenRefreshState.isRefreshing = false;
    }
  });
  
  return tokenRefreshState.refreshPromise;
};

/**
 * Determine the type of authentication error based on the response
 * 
 * @param {Response} response - The fetch Response object
 * @param {Object} data - The parsed response data
 * @returns {string} The error type from AUTH_ERROR_TYPES
 */
export const getAuthErrorType = (response, data) => {
  if (!response) {
    return AUTH_ERROR_TYPES.NETWORK_ERROR;
  }
  
  if (response.status === 401) {
    // Check for specific error messages in the response
    const errorMessage = data?.error || data?.message || '';
    if (errorMessage.toLowerCase().includes('expired')) {
      return AUTH_ERROR_TYPES.TOKEN_EXPIRED;
    }
    return AUTH_ERROR_TYPES.TOKEN_MISSING;
  }
  
  if (response.status === 403) {
    return AUTH_ERROR_TYPES.INSUFFICIENT_PERMISSIONS;
  }
  
  if (response.status >= 500) {
    return AUTH_ERROR_TYPES.SERVER_ERROR;
  }
  
  return AUTH_ERROR_TYPES.UNKNOWN_ERROR;
};

/**
 * Handle authentication errors with appropriate actions
 * 
 * @param {string} errorType - The error type from AUTH_ERROR_TYPES
 * @param {Object} options - Options for error handling
 * @param {boolean} options.redirect - Whether to redirect to login page
 * @param {boolean} options.notify - Whether to show a notification
 * @returns {void}
 */
export const handleAuthError = (errorType, { redirect = true, notify = true } = {}) => {
  const errorMessage = AUTH_ERROR_MESSAGES[errorType] || AUTH_ERROR_MESSAGES[AUTH_ERROR_TYPES.UNKNOWN_ERROR];
  
  // Show notification if enabled
  if (notify) {
    toast.error(errorMessage, {
      position: 'top-center',
      autoClose: 5000
    });
  }
  
  // Redirect to login page for authentication errors if enabled
  if (redirect && (
    errorType === AUTH_ERROR_TYPES.TOKEN_EXPIRED || 
    errorType === AUTH_ERROR_TYPES.TOKEN_MISSING
  )) {
    // Store the current path to redirect back after login
    if (typeof window !== 'undefined') {
      const currentPath = window.location.pathname;
      sessionStorage.setItem('auth_redirect_path', currentPath);
    }
    
    // Redirect to login page
    Router.push('/admin/login');
  }
};

/**
 * Make an authenticated fetch request with automatic token handling
 * 
 * @async
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @param {Object} authOptions - Authentication options
 * @param {boolean} authOptions.refresh - Whether to attempt token refresh on 401
 * @param {boolean} authOptions.redirect - Whether to redirect on auth failure
 * @param {boolean} authOptions.notify - Whether to show notifications
 * @returns {Promise<Object>} The response data
 * @throws {Error} If the request fails
 */
export const authenticatedFetch = async (
  url,
  options = {},
  { refresh = true, redirect = true, notify = true } = {}
) => {
  // Create controller for request cancellation
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout
  
  try {
    // Get auth token
    const token = await getAuthToken();
    
    // Prepare headers with authentication
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    // Prepare fetch options
    const fetchOptions = {
      ...options,
      headers,
      signal: controller.signal
    };
    
    // Make the request
    const response = await fetch(url, fetchOptions);
    
    // Parse the response
    let data;
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }
    
    // Handle successful response
    if (response.ok) {
      return data;
    }
    
    // Handle authentication errors
    const errorType = getAuthErrorType(response, data);
    
    // Try to refresh token and retry the request if it's an authentication error
    if (refresh && errorType === AUTH_ERROR_TYPES.TOKEN_EXPIRED) {
      const newToken = await refreshAuthToken();
      
      if (newToken) {
        // Retry the request with the new token
        return authenticatedFetch(
          url,
          {
            ...options,
            headers: {
              ...options.headers,
              'Authorization': `Bearer ${newToken}`
            }
          },
          { refresh: false, redirect, notify } // Prevent infinite refresh loop
        );
      }
    }
    
    // Handle the error if we couldn't refresh or it's not an auth error
    handleAuthError(errorType, { redirect, notify });
    
    // Throw an error with the appropriate message
    throw new Error(data.error || data.message || AUTH_ERROR_MESSAGES[errorType]);
  } catch (error) {
    // Handle network errors
    if (error.name === 'AbortError') {
      throw new Error('Request timed out. Please try again.');
    }
    
    if (error.message === 'Failed to fetch' || error.name === 'TypeError') {
      handleAuthError(AUTH_ERROR_TYPES.NETWORK_ERROR, { redirect: false, notify });
      throw new Error(AUTH_ERROR_MESSAGES[AUTH_ERROR_TYPES.NETWORK_ERROR]);
    }
    
    // Re-throw other errors
    throw error;
  } finally {
    // Clear the timeout
    clearTimeout(timeoutId);
  }
};

export default {
  authenticatedFetch,
  getAuthToken,
  refreshAuthToken,
  AUTH_ERROR_TYPES,
  AUTH_ERROR_MESSAGES
};
