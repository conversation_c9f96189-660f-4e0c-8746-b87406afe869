import { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import supabase, { getCurrentUser } from '@/lib/supabase';
import { toast } from 'react-toastify';

// Create context
const AuthContext = createContext();

/**
 * AuthProvider component that ensures authentication is maintained
 * across admin panel pages and API requests
 *
 * Uses the unified Supabase client for consistent authentication
 */
export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [role, setRole] = useState(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Non-admin pages that don't require authentication
  const publicPaths = [
    '/admin/login',
    '/admin/reset-password'
  ];

  // Check if current path requires authentication
  const isProtectedRoute = () => {
    return router.pathname.startsWith('/admin') &&
           !publicPaths.some(path => router.pathname.includes(path));
  };

  // Redirect to login page
  const redirectToLogin = () => {
    // Store the current path for redirect after login
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('redirect_after_login', router.pathname);
    }

    router.push('/admin/login');
  };

  // Initialize auth state
  useEffect(() => {
    let mounted = true;

    const initAuth = async () => {
      try {
        console.log('AuthProvider: Initializing authentication');

        // Get current user with role information
        const { user: currentUser, role: userRole } = await getCurrentUser();

        if (!mounted) return;

        if (currentUser) {
          setUser(currentUser);
          setRole(userRole);
          console.log(`AuthProvider: User authenticated - ${currentUser.email}, Role: ${userRole}`);
        } else {
          console.log('AuthProvider: No authenticated user');

          // Redirect to login if on protected admin page
          if (isProtectedRoute()) {
            redirectToLogin();
          }
        }
      } catch (err) {
        console.error('AuthProvider: Authentication error:', err);

        // Show detailed error notification based on error type
        if (err.message?.includes('JWT expired')) {
          toast.error('Your session has expired. Please login again.', {
            autoClose: 5000,
            position: 'top-center'
          });
        } else if (err.message?.includes('network')) {
          toast.error('Network error. Please check your connection and try again.', {
            autoClose: 5000,
            position: 'top-center'
          });
        } else if (err.message?.includes('timeout')) {
          toast.error('Authentication request timed out. Please try again.', {
            autoClose: 5000,
            position: 'top-center'
          });
        } else {
          toast.error('Authentication error. Please try again.', {
            autoClose: 5000,
            position: 'top-center'
          });
        }

        // Redirect to login if on protected admin page
        if (isProtectedRoute()) {
          redirectToLogin();
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    initAuth();

    // Subscribe to auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return;

        console.log(`AuthProvider: Auth event - ${event}`);

        try {
          if (event === 'SIGNED_IN' && session) {
            // Show success notification
            toast.success('Successfully signed in!', {
              autoClose: 3000,
              position: 'top-right'
            });

            // Get user role after sign in
            const { user: updatedUser, role: updatedRole } = await getCurrentUser();

            if (updatedUser) {
              setUser(updatedUser);
              setRole(updatedRole);
              console.log(`AuthProvider: User signed in - ${updatedUser.email}, Role: ${updatedRole}`);
            }
          } else if (event === 'SIGNED_OUT') {
            setUser(null);
            setRole(null);
            console.log('AuthProvider: User signed out');

            // Show notification
            toast.info('You have been signed out', {
              autoClose: 3000,
              position: 'top-right'
            });

            // Redirect to login if on protected admin page
            if (isProtectedRoute()) {
              redirectToLogin();
            }
          } else if (event === 'TOKEN_REFRESHED' && session) {
            console.log('AuthProvider: Token refreshed');
          } else if (event === 'USER_UPDATED' && session) {
            // Get updated user information
            const { user: updatedUser, role: updatedRole } = await getCurrentUser();

            if (updatedUser) {
              setUser(updatedUser);
              setRole(updatedRole);
              console.log(`AuthProvider: User updated - ${updatedUser.email}, Role: ${updatedRole}`);
            }
          } else if (event === 'PASSWORD_RECOVERY' && session) {
            // Show notification
            toast.info('Password recovery initiated. Please check your email.', {
              autoClose: 5000,
              position: 'top-center'
            });
          }
        } catch (error) {
          console.error('AuthProvider: Error handling auth state change:', error);

          // Show error notification
          toast.error('Error updating authentication state. Please refresh the page.', {
            autoClose: 5000,
            position: 'top-center'
          });
        }
      }
    );

    return () => {
      mounted = false;
      if (authListener?.subscription?.unsubscribe) {
        authListener.subscription.unsubscribe();
      }
    };
  }, [router]);

  // Provide auth context to children
  return (
    <AuthContext.Provider value={{ user, role, loading }}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthProvider;
