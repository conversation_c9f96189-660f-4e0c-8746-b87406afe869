import { getAdminClient, getCurrentUser, getClient } from '@/lib/supabase'

export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUser(req)
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' })
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' })
  }

  const { id } = req.query

  // Handle different HTTP methods
  switch (req.method) {
    case 'GET':
      return getAutomation(id, res)
    case 'PUT':
      return updateAutomation(id, req, res)
    case 'DELETE':
      // Only admins can delete automations
      try {
        const { role } = await getCurrentUser()
        if (role !== 'admin') {
          return res.status(403).json({ error: 'Forbidden' })
        }
      } catch (error) {
        return res.status(403).json({ error: 'Authorization failed' })
      }
      return deleteAutomation(id, res)
    default:
      return res.status(405).json({ error: 'Method not allowed' })
  }
}

// Get a single automation
async function getAutomation(id, res) {
  try {
    const client = getClient();
    if (!client) {
      console.error("Client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }

    // Get automation details
    const { data: automation, error } = await client
      .from('marketing_automations')
      .select(`
        *,
        template:template_id (id, name, template_type),
        segment:segment_id (id, name, description)
      `)
      .eq('id', id)
      .single()

    if (error) {
      throw error
    }

    if (!automation) {
      return res.status(404).json({ error: 'Automation not found' })
    }

    // Get execution logs
    const { data: logs, error: logsError } = await client
      .from('automation_logs')
      .select(`
        id,
        trigger_event,
        customer_id,
        status,
        message,
        sent_at,
        customers:customer_id (id, name, email)
      `)
      .eq('automation_id', id)
      .order('sent_at', { ascending: false })
      .limit(50)

    if (logsError) {
      console.error('Error fetching automation logs:', logsError)
    }

    // Get execution stats
    const { count: totalExecutions, error: totalError } = await client
      .from('automation_logs')
      .select('id', { count: 'exact' })
      .eq('automation_id', id)

    const { count: successfulExecutions, error: successError } = await client
      .from('automation_logs')
      .select('id', { count: 'exact' })
      .eq('automation_id', id)
      .eq('status', 'success')

    const stats = {
      total_executions: totalError ? 0 : totalExecutions || 0,
      successful_executions: successError ? 0 : successfulExecutions || 0,
      success_rate: totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0
    }

    return res.status(200).json({
      automation,
      logs: logs || [],
      stats
    })
  } catch (error) {
    console.error('Error fetching automation:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Update an automation
async function updateAutomation(id, req, res) {
  const {
    name,
    description,
    trigger_type,
    trigger_config,
    template_id,
    message_type,
    subject,
    content,
    segment_id,
    is_active
  } = req.body

  try {
    // Validate required fields
    if (!name || !trigger_type || !trigger_config || !message_type || !content) {
      return res.status(400).json({ error: 'Name, trigger type, trigger configuration, message type, and content are required' })
    }

    // Validate trigger type
    const validTriggerTypes = ['event', 'schedule', 'segment_entry']
    if (!validTriggerTypes.includes(trigger_type)) {
      return res.status(400).json({ error: 'Invalid trigger type. Must be one of: event, schedule, segment_entry' })
    }

    // Validate message type
    const validMessageTypes = ['email', 'sms', 'push']
    if (!validMessageTypes.includes(message_type)) {
      return res.status(400).json({ error: 'Invalid message type. Must be one of: email, sms, push' })
    }

    // Email messages require a subject
    if (message_type === 'email' && !subject) {
      return res.status(400).json({ error: 'Subject is required for email messages' })
    }

    // Validate trigger configuration
    try {
      validateTriggerConfig(trigger_type, trigger_config)
    } catch (validationError) {
      return res.status(400).json({ error: validationError.message })
    }

    // Update automation
    const { data, error } = await supabase
      .from('marketing_automations')
      .update({
        name,
        description,
        trigger_type,
        trigger_config,
        template_id,
        message_type,
        subject,
        content,
        segment_id,
        is_active,
        updated_at: new Date()
      })
      .eq('id', id)
      .select()

    if (error) {
      throw error
    }

    if (!data || data.length === 0) {
      return res.status(404).json({ error: 'Automation not found or you do not have permission to update it' })
    }

    return res.status(200).json(data[0])
  } catch (error) {
    console.error('Error updating automation:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Delete an automation
async function deleteAutomation(id, res) {
  try {
    // Delete automation logs first
    const { error: logsError } = await supabase
      .from('automation_logs')
      .delete()
      .eq('automation_id', id)

    if (logsError) {
      throw logsError
    }

    // Delete automation
    const { error } = await supabase
      .from('marketing_automations')
      .delete()
      .eq('id', id)

    if (error) {
      throw error
    }

    return res.status(200).json({ success: true })
  } catch (error) {
    console.error('Error deleting automation:', error)
    return res.status(500).json({ error: error.message })
  }
}

// Helper function to validate trigger configuration
function validateTriggerConfig(triggerType, triggerConfig) {
  if (typeof triggerConfig !== 'object') {
    throw new Error('Trigger configuration must be an object')
  }

  switch (triggerType) {
    case 'event':
      if (!triggerConfig.event_type) {
        throw new Error('Event type is required for event triggers')
      }
      break
    case 'schedule':
      if (!triggerConfig.frequency) {
        throw new Error('Frequency is required for schedule triggers')
      }
      break
    case 'segment_entry':
      if (!triggerConfig.delay_hours && triggerConfig.delay_hours !== 0) {
        throw new Error('Delay hours is required for segment entry triggers')
      }
      break
    default:
      throw new Error(`Unknown trigger type: ${triggerType}`)
  }
}
