import { getAdminClient, getCurrentUser } from '@/lib/supabase';

/**
 * API endpoint for admin analytics statistics
 * This endpoint uses service_role key to bypass RLS policies
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUser(req);
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' });
  }

  // Only allow GET requests for analytics
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {    const adminClient = getAdminClient();
    if (!adminClient) {
      console.error("Admin client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }
    
    const { timeframe = 'last30days' } = req.query;

    // Calculate date range based on timeframe
    const now = new Date();
    let startDate;

    switch (timeframe) {
      case 'last7days':
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'last90days':
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 90);
        break;
      case 'last30days':
      default:
        startDate = new Date(now);
        startDate.setDate(startDate.getDate() - 30);
        break;
    }

    const startDateIso = startDate.toISOString();
      // Get total bookings for the period
    const { count: totalBookings, error: bookingsError } = await adminClient
      .from('bookings')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDateIso);

    if (bookingsError) {
      throw bookingsError;
    }    // Get total orders and revenue for the period
    const { data: orders, error: ordersError } = await adminClient
      .from('orders')
      .select('id, total_amount')
      .gte('created_at', startDateIso);

    if (ordersError) {
      throw ordersError;
    }

    const totalOrders = orders?.length || 0;
    const totalRevenue = orders?.reduce((sum, order) => sum + (parseFloat(order.total_amount) || 0), 0) || 0;
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Get pageviews and sessions for conversion rate calculation
    const { data: analytics, error: analyticsError } = await supabaseAdmin
      .from('analytics')
      .select('sessions, page_views')
      .gte('date', startDateIso)
      .single();
      
    if (analyticsError && analyticsError.code !== 'PGRST116') { // Ignore not found error
      throw analyticsError;
    }

    const sessions = analytics?.sessions || 100; // Default value if no analytics
    const conversionRate = totalOrders / sessions;

    // Get top products
    const { data: topProducts, error: productsError } = await supabaseAdmin
      .from('order_items')
      .select(`
        product_id,
        products:product_id (name),
        quantity,
        price
      `)
      .join('orders', { foreignTable: 'orders', localColumn: 'order_id', foreignColumn: 'id' })
      .gte('orders.created_at', startDateIso);

    if (productsError) {
      throw productsError;
    }

    // Process and group products data
    const productMap = {};
    topProducts?.forEach(item => {
      const productId = item.product_id;
      const productName = item.products?.name || 'Unknown Product';
      const quantity = item.quantity || 0;
      const revenue = (item.price || 0) * quantity;

      if (!productMap[productId]) {
        productMap[productId] = {
          id: productId,
          name: productName,
          unitsSold: 0,
          revenue: 0
        };
      }

      productMap[productId].unitsSold += quantity;
      productMap[productId].revenue += revenue;
    });

    const processedTopProducts = Object.values(productMap)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // Get top services
    const { data: topServices, error: servicesError } = await supabaseAdmin
      .from('bookings')
      .select(`
        service_id,
        services:service_id (name),
        price
      `)
      .gte('created_at', startDateIso);

    if (servicesError) {
      throw servicesError;
    }

    // Process and group services data
    const serviceMap = {};
    topServices?.forEach(booking => {
      const serviceId = booking.service_id;
      const serviceName = booking.services?.name || 'Unknown Service';
      const revenue = booking.price || 0;

      if (!serviceMap[serviceId]) {
        serviceMap[serviceId] = {
          id: serviceId,
          name: serviceName,
          bookings: 0,
          revenue: 0
        };
      }

      serviceMap[serviceId].bookings += 1;
      serviceMap[serviceId].revenue += revenue;
    });

    const processedTopServices = Object.values(serviceMap)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // Return all analytics data
    return res.status(200).json({
      totalBookings,
      totalOrders,
      totalRevenue,
      averageOrderValue,
      conversionRate,
      topProducts: processedTopProducts,
      topServices: processedTopServices
    });
  } catch (error) {
    console.error('Error fetching analytics stats:', error);
    return res.status(500).json({ error: 'Server error', details: error.message });
  }
}
