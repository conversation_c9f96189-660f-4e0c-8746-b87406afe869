/**
 * Client-Side Authentication Auto-Recovery
 * 
 * This script automatically detects and fixes common authentication issues
 * without requiring manual intervention.
 * 
 * Features:
 * - Detects stuck authentication states
 * - Automatically clears corrupted session data
 * - Retries authentication with fresh tokens
 * - Monitors for server-side recovery signals
 */

class AuthAutoRecovery {
  constructor() {
    this.failureCount = 0;
    this.lastFailureTime = 0;
    this.maxFailures = 3;
    this.recoveryWindow = 5 * 60 * 1000; // 5 minutes
    this.isRecovering = false;
    
    // Start monitoring
    this.init();
  }

  init() {
    // Monitor authentication failures
    this.monitorAuthFailures();
    
    // Monitor server recovery signals
    this.monitorServerSignals();
    
    // Periodic health check
    this.startHealthCheck();
    
    console.log('🔄 Auth auto-recovery initialized');
  }

  monitorAuthFailures() {
    // Intercept failed API calls
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args);
        
        // Check for auth failures
        if (response.status === 401 || response.status === 403) {
          const url = args[0];
          if (typeof url === 'string' && url.includes('/api/admin')) {
            await this.handleAuthFailure();
          }
        }
        
        return response;
      } catch (error) {
        console.error('Fetch error:', error);
        throw error;
      }
    };
  }

  monitorServerSignals() {
    // Watch for server-side recovery headers
    const observer = new MutationObserver(() => {
      const metaTag = document.querySelector('meta[name="x-auth-recovery"]');
      if (metaTag && metaTag.content === 'clear-session') {
        this.executeRecovery();
        metaTag.remove();
      }
    });

    observer.observe(document.head, { childList: true });
  }

  async handleAuthFailure() {
    const now = Date.now();
    
    // Reset failure count if outside recovery window
    if (now - this.lastFailureTime > this.recoveryWindow) {
      this.failureCount = 0;
    }
    
    this.failureCount++;
    this.lastFailureTime = now;
    
    console.log(`⚠️ Auth failure detected (${this.failureCount}/${this.maxFailures})`);
    
    // Trigger recovery if threshold reached
    if (this.failureCount >= this.maxFailures && !this.isRecovering) {
      await this.executeRecovery();
    }
  }

  async executeRecovery() {
    if (this.isRecovering) return;
    
    this.isRecovering = true;
    console.log('🔧 Starting authentication recovery...');
    
    try {
      // Step 1: Clear all authentication data
      await this.clearAuthData();
      
      // Step 2: Clear browser storage
      this.clearBrowserStorage();
      
      // Step 3: Reset authentication state
      await this.resetAuthState();
      
      // Step 4: Attempt fresh authentication
      await this.attemptFreshAuth();
      
      console.log('✅ Authentication recovery completed');
      this.failureCount = 0;
      
    } catch (error) {
      console.error('❌ Recovery failed:', error);
    } finally {
      this.isRecovering = false;
    }
  }

  clearAuthData() {
    return new Promise((resolve) => {
      // Clear all known token types
      const tokenKeys = [
        'oss_auth_token',
        'sb_auth_token',
        'oss_session',
        'sb_session'
      ];

      // Clear localStorage
      tokenKeys.forEach(key => {
        localStorage.removeItem(key);
        localStorage.removeItem(`${key}_cache`);
      });

      // Clear sessionStorage
      tokenKeys.forEach(key => {
        sessionStorage.removeItem(key);
        sessionStorage.removeItem(`${key}_cache`);
      });

      // Clear cookies
      tokenKeys.forEach(key => {
        document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
        document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=${window.location.hostname}`;
      });

      console.log('🧹 Cleared authentication data');
      resolve();
    });
  }

  clearBrowserStorage() {
    // Clear any corrupted Supabase auth data
    const supabaseKeys = Object.keys(localStorage).filter(key => 
      key.startsWith('sb-') || key.includes('supabase')
    );
    
    supabaseKeys.forEach(key => {
      localStorage.removeItem(key);
    });

    console.log('🗄️ Cleared browser storage');
  }

  async resetAuthState() {
    // Reset any global auth state
    if (window.supabase) {
      try {
        await window.supabase.auth.signOut();
        console.log('🔓 Signed out from Supabase');
      } catch (error) {
        console.warn('Could not sign out from Supabase:', error);
      }
    }

    // Clear any auth context state
    if (window.authContext && window.authContext.reset) {
      window.authContext.reset();
      console.log('🔄 Reset auth context');
    }
  }

  async attemptFreshAuth() {
    // Try to get fresh authentication
    try {
      // Redirect to login if on admin page
      if (window.location.pathname.includes('/admin')) {
        console.log('🔄 Redirecting to login...');
        setTimeout(() => {
          window.location.href = '/admin/login';
        }, 1000);
      }
    } catch (error) {
      console.warn('Could not attempt fresh auth:', error);
    }
  }

  startHealthCheck() {
    // Periodic health check every 2 minutes
    setInterval(async () => {
      if (this.isRecovering) return;
      
      try {
        // Check if we can access admin API
        const response = await fetch('/api/admin/health', {
          method: 'GET',
          credentials: 'include'
        });
        
        if (!response.ok && response.status === 401) {
          console.log('🏥 Health check failed - authentication may be stuck');
          await this.handleAuthFailure();
        }
      } catch (error) {
        // Ignore network errors
      }
    }, 2 * 60 * 1000); // 2 minutes
  }

  // Manual recovery trigger
  async trigger() {
    console.log('🔧 Manual recovery triggered');
    await this.executeRecovery();
  }
}

// Initialize auto-recovery
const authAutoRecovery = new AuthAutoRecovery();

// Make it globally available for manual triggering
window.authAutoRecovery = authAutoRecovery;

// Add manual recovery button for development
if (process.env.NODE_ENV === 'development') {
  const addRecoveryButton = () => {
    if (document.getElementById('auth-recovery-btn')) return;
    
    const button = document.createElement('button');
    button.id = 'auth-recovery-btn';
    button.innerHTML = '🔧 Fix Auth';
    button.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 9999;
      padding: 8px 12px;
      background: #ff6b6b;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    `;
    
    button.onclick = () => authAutoRecovery.trigger();
    document.body.appendChild(button);
  };

  // Add button when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addRecoveryButton);
  } else {
    addRecoveryButton();
  }
}

// Make the function available globally instead of using ES6 export
window.authAutoRecovery = authAutoRecovery;
