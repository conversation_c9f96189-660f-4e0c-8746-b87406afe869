/**
 * Authentication White Screen Recovery Script
 * 
 * This script specifically addresses the white screen issue where users get stuck
 * on "authenticating" and provides automatic recovery mechanisms.
 */

(function() {
  'use strict';

  let recoveryAttempts = 0;
  const MAX_RECOVERY_ATTEMPTS = 3;
  const RECOVERY_TIMEOUT = 10000; // 10 seconds
  let authTimeoutId = null;
  let isRecovering = false;

  // Debug logging with timestamp
  function authLog(message, ...args) {
    const timestamp = new Date().toISOString();
    console.log(`[Auth Recovery ${timestamp}]`, message, ...args);
  }

  // Check if we're stuck in authentication
  function isStuckInAuth() {
    // Check for common signs of being stuck
    const authIndicators = [
      document.querySelector('[data-testid="auth-loading"]'),
      document.querySelector('.auth-loading'),
      document.querySelector('[class*="authenticating"]'),
      document.querySelector('[class*="loading"]')
    ];

    const hasAuthIndicator = authIndicators.some(el => el !== null);
    const hasWhiteScreen = document.body.children.length === 0 || 
                          (document.body.textContent || '').trim() === '';
    
    return hasAuthIndicator || hasWhiteScreen;
  }

  // Clear all authentication data
  function clearAuthData() {
    authLog('Clearing all authentication data...');
    
    try {
      // Clear localStorage
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (
          key.includes('auth') || 
          key.includes('supabase') || 
          key.includes('token') ||
          key.includes('session') ||
          key.includes('oss_')
        )) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));
      
      // Clear sessionStorage
      const sessionKeysToRemove = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (
          key.includes('auth') || 
          key.includes('supabase') || 
          key.includes('token') ||
          key.includes('session') ||
          key.includes('oss_')
        )) {
          sessionKeysToRemove.push(key);
        }
      }
      sessionKeysToRemove.forEach(key => sessionStorage.removeItem(key));
      
      // Clear cookies
      document.cookie.split(";").forEach(function(c) { 
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
      });
      
      authLog('Authentication data cleared successfully');
    } catch (error) {
      authLog('Error clearing auth data:', error);
    }
  }

  // Force reload with cache bust
  function forceReload() {
    authLog('Forcing page reload with cache bust...');
    const url = new URL(window.location);
    url.searchParams.set('_auth_recovery', Date.now());
    window.location.href = url.toString();
  }

  // Attempt recovery
  function attemptRecovery() {
    if (isRecovering) {
      authLog('Recovery already in progress, skipping...');
      return;
    }

    recoveryAttempts++;
    isRecovering = true;
    
    authLog(`Starting recovery attempt ${recoveryAttempts}/${MAX_RECOVERY_ATTEMPTS}`);

    if (recoveryAttempts >= MAX_RECOVERY_ATTEMPTS) {
      authLog('Max recovery attempts reached, forcing hard reload...');
      clearAuthData();
      setTimeout(forceReload, 1000);
      return;
    }

    // Step 1: Clear auth data
    clearAuthData();

    // Step 2: Try to reset Supabase client if available
    if (window.supabase) {
      authLog('Attempting to sign out from Supabase...');
      try {
        window.supabase.auth.signOut().then(() => {
          authLog('Supabase sign out completed');
        }).catch(error => {
          authLog('Supabase sign out error:', error);
        });
      } catch (error) {
        authLog('Error during Supabase sign out:', error);
      }
    }

    // Step 3: Wait and check if recovery worked
    setTimeout(() => {
      isRecovering = false;
      if (isStuckInAuth()) {
        authLog('Recovery attempt failed, trying again...');
        setTimeout(attemptRecovery, 2000);
      } else {
        authLog('Recovery appears successful');
        recoveryAttempts = 0;
      }
    }, 3000);
  }

  // Monitor for white screen / stuck auth
  function startMonitoring() {
    authLog('Starting white screen monitoring...');

    // Check immediately
    if (isStuckInAuth()) {
      authLog('Detected stuck authentication state on load');
      setTimeout(attemptRecovery, 2000);
    }

    // Set up timeout for authentication
    authTimeoutId = setTimeout(() => {
      if (isStuckInAuth()) {
        authLog('Authentication timeout detected, starting recovery...');
        attemptRecovery();
      }
    }, RECOVERY_TIMEOUT);

    // Monitor for DOM changes that might indicate stuck state
    const observer = new MutationObserver((mutations) => {
      // Clear existing timeout
      if (authTimeoutId) {
        clearTimeout(authTimeoutId);
      }

      // Check if we're still stuck
      if (isStuckInAuth()) {
        authTimeoutId = setTimeout(() => {
          authLog('Stuck state detected via DOM mutation, starting recovery...');
          attemptRecovery();
        }, RECOVERY_TIMEOUT);
      } else {
        // We're not stuck, reset recovery attempts
        recoveryAttempts = 0;
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'data-testid']
    });
  }

  // Add manual recovery button for development
  function addRecoveryButton() {
    if (process.env.NODE_ENV !== 'development' && !window.location.hostname.includes('localhost')) {
      return;
    }

    const button = document.createElement('button');
    button.textContent = '🔧 Fix Auth';
    button.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 10000;
      background: #ff4444;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      font-family: monospace;
    `;
    
    button.onclick = () => {
      authLog('Manual recovery triggered');
      recoveryAttempts = 0; // Reset attempts for manual trigger
      attemptRecovery();
    };
    
    document.body.appendChild(button);
    authLog('Manual recovery button added');
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      startMonitoring();
      addRecoveryButton();
    });
  } else {
    startMonitoring();
    addRecoveryButton();
  }

  // Expose recovery function globally for manual use
  window.authWhiteScreenRecovery = {
    attempt: attemptRecovery,
    clear: clearAuthData,
    reload: forceReload,
    isStuck: isStuckInAuth
  };

  authLog('White screen recovery system initialized');
})();
