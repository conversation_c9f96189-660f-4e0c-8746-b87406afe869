import { getAdminClient, getCurrentUser } from '@/lib/supabase';

/**
 * API endpoint for admin marketing dashboard statistics
 * This endpoint uses service_role key to bypass RLS policies
 *
 * @param {Object} req - HTTP request object
 * @param {Object} res - HTTP response object
 * @returns {Object} - JSON response
 */
export default async function handler(req, res) {
  // Check authentication
  try {
    const { user, role } = await getCurrentUser(req);
    if (!user || (role !== 'admin' && role !== 'staff')) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
  } catch (error) {
    return res.status(401).json({ error: 'Authentication failed' });
  }

  // Only allow GET requests for stats
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  try {    // Get admin client
    const adminClient = getAdminClient();
    if (!adminClient) {
      console.error("Admin client not available.");
      return res.status(500).json({ error: 'Database connection failed' });
    }
    
    // Get segments count
    const { count: totalSegments, error: segmentsError } = await adminClient
      .from('segments')
      .select('*', { count: 'exact', head: true });

    if (segmentsError) {
      throw segmentsError;
    }    // Get campaigns count
    const { count: totalCampaigns, error: campaignsError } = await adminClient
      .from('campaigns')
      .select('*', { count: 'exact', head: true });

    if (campaignsError) {
      throw campaignsError;
    }

    // Get active campaigns count
    const { count: activeCampaigns, error: activeCampaignsError } = await adminClient
      .from('campaigns')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active');

    if (activeCampaignsError) {
      throw activeCampaignsError;
    }    // Get customers with marketing consent
    const { count: customersWithConsent, error: consentError } = await adminClient
      .from('customer_preferences')
      .select('*', { count: 'exact', head: true })
      .eq('marketing_consent', true);

    if (consentError) {
      throw consentError;
    }

    // Get total customers
    const { count: totalCustomers, error: customersError } = await adminClient
      .from('customers')
      .select('*', { count: 'exact', head: true });

    if (customersError) {
      throw customersError;
    }

    // Return all stats
    return res.status(200).json({
      totalSegments,
      totalCampaigns,
      activeCampaigns,
      customersWithConsent,
      totalCustomers
    });
  } catch (error) {
    console.error('Error fetching marketing stats:', error);
    return res.status(500).json({ error: 'Server error', details: error.message });
  }
}
