/**
 * Browser Extension Error Suppression - Enhanced Version
 * 
 * This script aggressively suppresses browser extension errors that can interfere
 * with the application, including runtime.lastError messages.
 */

(function() {
  'use strict';

  // Suppress console errors from extensions
  const originalConsoleError = console.error;
  console.error = function(...args) {
    const message = args.join(' ');
    
    // Suppress specific extension error patterns
    const suppressedPatterns = [
      'runtime.lastError',
      'message port closed',
      'Extension context invalidated',
      'chrome-extension://',
      'moz-extension://',
      'Unchecked runtime.lastError'
    ];
    
    if (suppressedPatterns.some(pattern => message.includes(pattern))) {
      console.debug('[Extension Error Suppressed]:', message);
      return;
    }
    
    // Call original console.error for legitimate errors
    return originalConsoleError.apply(console, args);
  };

  // Suppress runtime.lastError at the Chrome API level
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    // Override chrome.runtime.lastError getter
    let lastErrorValue = null;
    Object.defineProperty(chrome.runtime, 'lastError', {
      get: function() {
        const error = lastErrorValue;
        lastErrorValue = null; // Clear after reading
        return error;
      },
      set: function(value) {
        if (value && value.message && value.message.includes('message port closed')) {
          console.debug('[Chrome Runtime Error Suppressed]:', value.message);
          return; // Don't set the error
        }
        lastErrorValue = value;
      },
      configurable: true
    });

    // Enhance message listener error handling
    const originalAddListener = chrome.runtime.onMessage.addListener;
    if (originalAddListener) {
      chrome.runtime.onMessage.addListener = function(callback) {
        return originalAddListener.call(this, function(...args) {
          try {
            return callback(...args);
          } catch (error) {
            if (error.message && error.message.includes('message port closed')) {
              console.debug('[Extension Message Error Suppressed]:', error.message);
              return;
            }
            throw error;
          }
        });
      };
    }
  }

  // Global error handler for unhandled extension errors
  window.addEventListener('error', function(event) {
    const error = event.error;
    if (error && error.message) {
      // Suppress common extension-related errors
      const suppressedErrors = [
        'message port closed',
        'Extension context invalidated',
        'runtime.lastError',
        'Cannot read property of undefined',
        'chrome-extension://',
        'moz-extension://'
      ];

      if (suppressedErrors.some(pattern => error.message.includes(pattern))) {
        console.debug('[Extension Error Suppressed]:', error.message);
        event.preventDefault();
        return false;
      }
    }
  });

  // Suppress unhandled promise rejections from extensions
  window.addEventListener('unhandledrejection', function(event) {
    if (event.reason && event.reason.message) {
      const suppressedErrors = [
        'message port closed',
        'Extension context invalidated',
        'chrome-extension://',
        'moz-extension://'
      ];

      if (suppressedErrors.some(pattern => event.reason.message.includes(pattern))) {
        console.debug('[Extension Promise Rejection Suppressed]:', event.reason.message);
        event.preventDefault();
        return false;
      }
    }
  });

  console.debug('🔇 Browser extension error suppression initialized');
})();
