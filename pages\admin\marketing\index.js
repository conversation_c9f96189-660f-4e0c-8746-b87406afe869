import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import styles from '@/styles/admin/marketing/MarketingDashboard.module.css'

export default function MarketingDashboard() {
  const router = useRouter()
  const [stats, setStats] = useState({
    totalSegments: 0,
    totalCampaigns: 0,
    activeCampaigns: 0,
    customersWithConsent: 0,
    totalCustomers: 0
  })
  const [recentSegments, setRecentSegments] = useState([])
  const [recentCampaigns, setRecentCampaigns] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Fetch marketing dashboard data
  useEffect(() => {
    let isMounted = true;
    const controller = new AbortController();

    const fetchDashboardData = async () => {
      if (!isMounted) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch data with timeouts to prevent hanging requests
        const fetchWithTimeout = async (url, options = {}) => {
          const timeout = options.timeout || 5000;
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error(`Request to ${url} timed out`)), timeout);
          });

          try {
            const fetchPromise = fetch(url, {
              ...options,
              signal: controller.signal
            });
            const response = await Promise.race([fetchPromise, timeoutPromise]);

            if (!response.ok) {
              const errorData = await response.json().catch(() => ({ error: `HTTP error ${response.status}` }));
              throw new Error(errorData.error || `Failed with status ${response.status}`);
            }

            return response;
          } catch (error) {
            console.error(`Error fetching ${url}:`, error);
            throw error;
          }
        };

        // Fetch segments with error handling
        try {
          const segmentsResponse = await fetchWithTimeout('/api/admin/marketing/segments?limit=5&sort_by=created_at&sort_order=desc');
          if (isMounted) {
            const segmentsData = await segmentsResponse.json();
            setRecentSegments(segmentsData.segments || []);
          }
        } catch (segmentsError) {
          console.error('Error fetching segments:', segmentsError);
          // Continue with other requests even if segments fail
        }

        // Fetch stats with error handling
        try {
          const statsResponse = await fetchWithTimeout('/api/admin/marketing/stats');
          if (isMounted) {
            const statsData = await statsResponse.json();
            setStats(statsData);
          }
        } catch (statsError) {
          console.error('Error fetching stats:', statsError);
          // Continue with other requests even if stats fail
        }

        // Fetch campaigns with error handling
        try {
          const campaignsResponse = await fetchWithTimeout('/api/admin/marketing/campaigns?limit=3&sort_by=created_at&sort_order=desc');
          if (isMounted) {
            const campaignsData = await campaignsResponse.json();
            setRecentCampaigns(campaignsData.campaigns || []);
          }
        } catch (campaignsError) {
          console.error('Error fetching campaigns:', campaignsError);
          // Continue even if campaigns fail
        }

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        if (isMounted) {
          setError(error.message || 'Failed to load dashboard data');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchDashboardData();

    // Cleanup function to prevent memory leaks
    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [])

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'Ongoing'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Calculate open rate
  const calculateOpenRate = (campaign) => {
    if (!campaign.metrics || !campaign.metrics.sent || campaign.metrics.sent === 0) {
      return '0%'
    }
    return `${Math.round((campaign.metrics.opened / campaign.metrics.sent) * 100)}%`
  }

  // Calculate click rate
  const calculateClickRate = (campaign) => {
    if (!campaign.metrics || !campaign.metrics.opened || campaign.metrics.opened === 0) {
      return '0%'
    }
    return `${Math.round((campaign.metrics.clicked / campaign.metrics.opened) * 100)}%`
  }

  // Get status class
  const getStatusClass = (status) => {
    switch (status) {
      case 'active':
        return styles.statusActive
      case 'scheduled':
        return styles.statusScheduled
      case 'completed':
        return styles.statusCompleted
      case 'draft':
        return styles.statusDraft
      default:
        return ''
    }
  }

  return (
    <AdminLayout>
      <div className={styles.marketingDashboard}>
        <div className={styles.header}>
          <h2>Marketing Dashboard</h2>
          <div className={styles.actions}>
            <Link href="/admin/marketing/dashboard" className={styles.analyticsButton}>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="20" x2="18" y2="10"></line>
                <line x1="12" y1="20" x2="12" y2="4"></line>
                <line x1="6" y1="20" x2="6" y2="14"></line>
              </svg>
              Advanced Analytics
            </Link>
          </div>
        </div>

        {error && (
          <div className={styles.error}>
            Error: {error}
          </div>
        )}

        {loading ? (
          <div className={styles.loading}>Loading dashboard data...</div>
        ) : (
          <>
            <div className={styles.statsGrid}>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{stats.totalSegments}</div>
                <div className={styles.statLabel}>Customer Segments</div>
                <Link href="/admin/marketing/segments" className={styles.statLink}>
                  View All
                </Link>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{stats.totalCampaigns}</div>
                <div className={styles.statLabel}>Total Campaigns</div>
                <Link href="/admin/marketing/campaigns" className={styles.statLink}>
                  View All
                </Link>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{stats.activeCampaigns}</div>
                <div className={styles.statLabel}>Active Campaigns</div>
                <Link href="/admin/marketing/campaigns?status=active" className={styles.statLink}>
                  View Active
                </Link>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statValue}>
                  {stats.customersWithConsent}/{stats.totalCustomers}
                </div>
                <div className={styles.statLabel}>Marketing Consent</div>
                <Link href="/admin/customers?marketing_consent=true" className={styles.statLink}>
                  View Customers
                </Link>
              </div>
            </div>

            <div className={styles.dashboardGrid}>
              <div className={styles.dashboardSection}>
                <div className={styles.sectionHeader}>
                  <h3>Recent Segments</h3>
                  <Link href="/admin/marketing/segments" className={styles.viewAllLink}>
                    View All
                  </Link>
                </div>
                {recentSegments.length === 0 ? (
                  <div className={styles.emptyState}>
                    <p>No segments created yet</p>
                    <Link href="/admin/marketing/segments/new" className={styles.createButton}>
                      Create Segment
                    </Link>
                  </div>
                ) : (
                  <div className={styles.segmentList}>
                    {recentSegments.map((segment) => (
                      <Link href={`/admin/marketing/segments/${segment.id}`} key={segment.id} className={styles.segmentCard}>
                        <div className={styles.segmentName}>{segment.name}</div>
                        <div className={styles.segmentMeta}>
                          <span>{segment.customer_count || 0} customers</span>
                          <span>Created {formatDate(segment.created_at)}</span>
                        </div>
                      </Link>
                    ))}
                  </div>
                )}
              </div>

              <div className={styles.dashboardSection}>
                <div className={styles.sectionHeader}>
                  <h3>Active Campaigns</h3>
                  <Link href="/admin/marketing/campaigns" className={styles.viewAllLink}>
                    View All
                  </Link>
                </div>
                {recentCampaigns.length === 0 ? (
                  <div className={styles.emptyState}>
                    <p>No campaigns created yet</p>
                    <Link href="/admin/marketing/campaigns/new" className={styles.createButton}>
                      Create Campaign
                    </Link>
                  </div>
                ) : (
                  <div className={styles.campaignList}>
                    {recentCampaigns.map((campaign) => (
                      <Link href={`/admin/marketing/campaigns/${campaign.id}`} key={campaign.id} className={styles.campaignCard}>
                        <div className={styles.campaignHeader}>
                          <div className={styles.campaignName}>{campaign.name}</div>
                          <div className={`${styles.campaignStatus} ${getStatusClass(campaign.status)}`}>
                            {campaign.status}
                          </div>
                        </div>
                        <div className={styles.campaignType}>
                          {campaign.campaign_type === 'email' ? 'Email Campaign' : 'SMS Campaign'}
                        </div>
                        <div className={styles.campaignDates}>
                          {formatDate(campaign.start_date)} - {formatDate(campaign.end_date)}
                        </div>
                        <div className={styles.campaignMetrics}>
                          {campaign.campaign_type === 'email' && (
                            <>
                              <div className={styles.metricItem}>
                                <span className={styles.metricLabel}>Sent:</span>
                                <span className={styles.metricValue}>{campaign.metrics.sent}</span>
                              </div>
                              <div className={styles.metricItem}>
                                <span className={styles.metricLabel}>Open Rate:</span>
                                <span className={styles.metricValue}>{calculateOpenRate(campaign)}</span>
                              </div>
                              <div className={styles.metricItem}>
                                <span className={styles.metricLabel}>Click Rate:</span>
                                <span className={styles.metricValue}>{calculateClickRate(campaign)}</span>
                              </div>
                            </>
                          )}
                          {campaign.campaign_type === 'sms' && (
                            <>
                              <div className={styles.metricItem}>
                                <span className={styles.metricLabel}>Sent:</span>
                                <span className={styles.metricValue}>{campaign.metrics.sent}</span>
                              </div>
                              <div className={styles.metricItem}>
                                <span className={styles.metricLabel}>Responded:</span>
                                <span className={styles.metricValue}>{campaign.metrics.responded}</span>
                              </div>
                            </>
                          )}
                        </div>
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div className={styles.actionButtons}>
              <Link href="/admin/marketing/segments/new" className={styles.actionButton}>
                Create Segment
              </Link>
              <Link href="/admin/marketing/campaigns/new" className={styles.actionButton}>
                Create Campaign
              </Link>
              <Link href="/admin/marketing/templates" className={styles.actionButton}>
                Manage Templates
              </Link>
              <Link href="/admin/marketing/automations" className={styles.actionButton}>
                Automated Messages
              </Link>
            </div>
          </>
        )}
      </div>
    </AdminLayout>
  )
}
